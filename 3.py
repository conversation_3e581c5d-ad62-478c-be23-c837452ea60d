import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import random
from collections import deque

# Grid Environment
class DroneEnvironment:
    def __init__(self, grid_size, num_drones, obstacles):
        self.grid_size = grid_size
        self.num_drones = num_drones
        self.obstacles = set(obstacles)  # Obstacle positions as (x, y) tuples
        self.drones = []  # List of (x, y) positions for drones
        self.targets = []  # List of (x, y) target positions for drones
        self.reset()

    def reset(self):
        """Reset the environment with random drone and target positions."""
        self.drones = [(i, 0) for i in range(self.num_drones)]  # Start at y=0
        self.targets = [(i, self.grid_size-1) for i in range(self.num_drones)]  # Targets at top
        return [self.get_state(i) for i in range(self.num_drones)]

    def step(self, actions):
        """Execute actions for all drones and return states, rewards, done."""
        rewards = []
        next_states = []
        done = True
        new_positions = []
        
        # Calculate new positions
        for i, (action, (x, y)) in enumerate(zip(actions, self.drones)):
            if action == 0:  # Up
                new_x, new_y = x, min(y + 1, self.grid_size - 1)
            elif action == 1:  # Down
                new_x, new_y = x, max(y - 1, 0)
            elif action == 2:  # Left
                new_x, new_y = max(x - 1, 0), y
            else:  # Right
                new_x, new_y = min(x + 1, self.grid_size - 1), y
            new_positions.append((new_x, new_y))

        # Check collisions and assign rewards
        occupied = set(new_positions)
        for i, new_pos in enumerate(new_positions):
            reward = -0.1  # Small penalty per step
            if new_pos in self.obstacles or (new_pos in occupied and new_positions.count(new_pos) > 1):
                reward = -10  # Collision penalty
            elif new_pos == self.targets[i]:
                reward = 10  # Target reached reward
            else:
                done = False  # Not all drones have reached targets
            rewards.append(reward)
            self.drones[i] = new_pos if reward != -10 else self.drones[i]  # Don't move if collision

        next_states = [self.get_state(i) for i in range(self.num_drones)]
        return next_states, rewards, done

    def get_state(self, drone_id):
        """Return state vector for a drone."""
        x, y = self.drones[drone_id]
        tx, ty = self.targets[drone_id]
        state = [x, y, tx, ty]
        for obs in self.obstacles:
            state.extend(obs)
        for i, (dx, dy) in enumerate(self.drones):
            if i != drone_id:
                state.extend([dx, dy])
        return np.array(state)

# DDQN Network
class DDQN(nn.Module):
    def __init__(self, state_dim, action_dim):
        super(DDQN, self).__init__()
        self.fc1 = nn.Linear(state_dim, 128)
        self.fc2 = nn.Linear(128, 128)
        self.fc3 = nn.Linear(128, action_dim)

    def forward(self, x):
        x = torch.relu(self.fc1(x))
        x = torch.relu(self.fc2(x))
        return self.fc3(x)

# Drone Agent
class DroneAgent:
    def __init__(self, state_dim, action_dim):
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.epsilon = 0.1
        self.gamma = 0.99
        self.main_net = DDQN(state_dim, action_dim)
        self.target_net = DDQN(state_dim, action_dim)
        self.target_net.load_state_dict(self.main_net.state_dict())
        self.optimizer = optim.Adam(self.main_net.parameters(), lr=0.001)
        self.memory = deque(maxlen=10000)

    def select_action(self, state):
        """Choose an action using epsilon-greedy policy."""
        if random.random() < self.epsilon:
            return random.randint(0, self.action_dim - 1)
        state = torch.FloatTensor(state).unsqueeze(0)
        with torch.no_grad():
            q_values = self.main_net(state)
        return q_values.argmax().item()

    def store_experience(self, state, action, reward, next_state, done):
        """Store experience in replay memory."""
        self.memory.append((state, action, reward, next_state, done))

    def train(self, batch_size):
        """Train the main network using a batch from memory."""
        if len(self.memory) < batch_size:
            return
        batch = random.sample(self.memory, batch_size)
        states, actions, rewards, next_states, dones = zip(*batch)
        
        states = torch.FloatTensor(states)
        actions = torch.LongTensor(actions)
        rewards = torch.FloatTensor(rewards)
        next_states = torch.FloatTensor(next_states)
        dones = torch.FloatTensor(dones)

        q_values = self.main_net(states).gather(1, actions.unsqueeze(1)).squeeze(1)
        next_actions = self.main_net(next_states).argmax(1)
        next_q_values = self.target_net(next_states).gather(1, next_actions.unsqueeze(1)).squeeze(1)
        targets = rewards + (1 - dones) * self.gamma * next_q_values

        loss = nn.MSELoss()(q_values, targets.detach())
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()

    def update_target(self):
        """Update target network with main network weights."""
        self.target_net.load_state_dict(self.main_net.state_dict())

# Main Training Loop
def main():
    grid_size = 10
    num_drones = 4
    obstacles = [(3, 3), (4, 4), (5, 5)]
    env = DroneEnvironment(grid_size, num_drones, obstacles)
    state_dim = 4 + 2 * len(obstacles) + 2 * (num_drones - 1)  # [x, y, tx, ty, obstacles, other drones]
    action_dim = 4  # Up, Down, Left, Right

    agents = [DroneAgent(state_dim, action_dim) for _ in range(num_drones)]
    episodes = 500
    batch_size = 64
    update_freq = 10

    for episode in range(episodes):
        states = env.reset()
        done = False
        total_rewards = [0] * num_drones
        step = 0
        
        while not done:
            actions = [agent.select_action(states[i]) for i, agent in enumerate(agents)]
            next_states, rewards, done = env.step(actions)
            
            for i, agent in enumerate(agents):
                agent.store_experience(states[i], actions[i], rewards[i], next_states[i], done)
                agent.train(batch_size)
                total_rewards[i] += rewards[i]
            
            states = next_states
            step += 1
            if step % update_freq == 0:
                for agent in agents:
                    agent.update_target()

        if episode % 50 == 0:
            print(f"Episode {episode}, Rewards: {total_rewards}")

if __name__ == "__main__":
    main()